# 电机控制故障排查指南

## 🚨 问题概述
基于项目架构分析，您的STM32F407VET6电机控制系统无法控制电机运动。本文档提供系统性的故障排查方法。

## 📋 快速诊断清单

### ✅ 最可能的原因（按优先级排序）

#### 1. **控制任务未运行** ⭐⭐⭐⭐⭐
**问题**: 在`my_scheduler.c`中，`PID_Task`被注释掉了
```c
// 第13行被注释
// {PID_Task,5,0},
```

**解决方案**:
```c
task all_task[]={
    {led_task,1,0},
    {Gray_Task,5,0},        // 取消注释
    {Encoder_Task,5,0},     // 取消注释  
    {PID_Task,5,0},         // 取消注释 - 关键！
    {bno080_task,10,0},     // 取消注释
    {uart_task,5,0},
    {key_task,10,0},
    {oled_task,100,0},
};
```

#### 2. **电机未被主动控制** ⭐⭐⭐⭐
**问题**: 没有代码调用`motor_set_l()`或`motor_set_r()`函数

**检查方法**:
```c
// 在main.c的while循环中添加测试代码
motor_set_l(50.0f);  // 左电机50%速度
motor_set_r(50.0f);  // 右电机50%速度
HAL_Delay(2000);
motor_break();       // 停止
HAL_Delay(1000);
```

#### 3. **PWM信号未输出** ⭐⭐⭐
**问题**: TIM1 PWM通道可能未正确启动

**检查方法**:
- 用示波器测量PE9, PE11, PE13, PE14引脚的PWM信号
- 检查TIM1时钟是否使能

**解决方案**:
```c
// 在Motor_Init()后添加
HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_2);
HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_3);
HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_4);
```

## 🔧 系统性故障排查

### 软件层面检查

#### A. 任务调度系统
1. **验证任务数量计算**:
```c
// 在all_task_init()中添加调试
my_printf(&huart1, "Task count: %d\n", task_num);
```

2. **验证任务执行**:
```c
// 在PID_Task函数中添加
void PID_Task(void) {
    static uint32_t counter = 0;
    if(++counter % 100 == 0) {
        my_printf(&huart1, "PID Task running: %lu\n", counter);
    }
    // PID控制逻辑
}
```

#### B. 电机驱动检查
1. **验证电机初始化**:
```c
// 在Motor_Init()后添加
my_printf(&huart1, "Motor initialized\n");
my_printf(&huart1, "Left motor state: %d\n", Motor_GetState(&left_motor));
my_printf(&huart1, "Right motor state: %d\n", Motor_GetState(&right_motor));
```

2. **测试电机使能状态**:
```c
Motor_Enable(&left_motor, 1);
Motor_Enable(&right_motor, 1);
```

#### C. PWM配置验证
1. **检查PWM周期和占空比**:
```c
// 检查TIM1配置
my_printf(&huart1, "TIM1 ARR: %lu\n", htim1.Instance->ARR);
my_printf(&huart1, "TIM1 PSC: %lu\n", htim1.Instance->PSC);
```

2. **手动设置PWM测试**:
```c
// 直接设置PWM寄存器测试
htim1.Instance->CCR1 = 500;  // 50%占空比
htim1.Instance->CCR2 = 500;
htim1.Instance->CCR3 = 500;
htim1.Instance->CCR4 = 500;
```

### 硬件层面检查

#### A. 电源系统
- [ ] 检查5V电源是否稳定
- [ ] 检查3.3V逻辑电源
- [ ] 测量电机驱动板供电电压
- [ ] 检查接地连接

#### B. 信号连接
- [ ] 验证PE9→AIN1_L (TIM1_CH1)
- [ ] 验证PE11→AIN2_L (TIM1_CH2)  
- [ ] 验证PE13→AIN1_R (TIM1_CH3)
- [ ] 验证PE14→AIN2_R (TIM1_CH4)

#### C. DRV8871驱动板检查
1. **最小PWM阈值问题**:
```c
// DRV8871需要最小启动电流
// 快衰减模式：最小5% (50/999)
// 慢衰减模式：最小60% (599/999)
Motor_SetDecayMode(&left_motor, MOTOR_DECAY_FAST);
Motor_SetSpeed(&left_motor, 10.0f);  // 测试10%速度
```

2. **驱动板故障检测**:
- 测量AIN1/AIN2引脚电压
- 检查电机输出端电压
- 验证驱动板使能信号

## 🛠️ 调试步骤

### 第一步：恢复控制任务
```c
// 1. 取消注释my_scheduler.c中的PID_Task
// 2. 确保PID_Task函数存在且有实际控制逻辑
// 3. 重新编译并下载程序
```

### 第二步：添加调试输出
```c
void motor_debug_test(void) {
    my_printf(&huart1, "=== Motor Debug Test ===\n");
    
    // 测试左电机
    my_printf(&huart1, "Testing left motor...\n");
    motor_set_l(30.0f);
    HAL_Delay(2000);
    motor_set_l(0.0f);
    
    // 测试右电机  
    my_printf(&huart1, "Testing right motor...\n");
    motor_set_r(30.0f);
    HAL_Delay(2000);
    motor_set_r(0.0f);
    
    my_printf(&huart1, "Test completed\n");
}
```

### 第三步：硬件信号验证
1. 用万用表测量PWM引脚电压（应该有变化）
2. 用示波器观察PWM波形（频率约12kHz）
3. 检查电机驱动板输出电压

### 第四步：逐步增加复杂度
1. 先测试单个电机
2. 再测试双电机
3. 最后集成PID控制

## 🔍 常见问题解答

### Q1: 电机有声音但不转动
**原因**: PWM占空比太低，无法克服静摩擦
**解决**: 增加最小PWM阈值到60%以上

### Q2: 一个电机转另一个不转
**原因**: 硬件连接或驱动板问题
**解决**: 交换连接线测试，排查硬件故障

### Q3: 电机转向错误
**原因**: 电机安装方向设置错误
**解决**: 修改Motor_Create()中的reverse参数

### Q4: PWM信号正常但电机不转
**原因**: 
- 电机驱动板故障
- 电源电压不足
- 电机负载过大

## 📞 紧急解决方案

如果以上方法都无效，尝试最简单的测试：

```c
// 在main.c的while(1)循环中直接控制
while (1) {
    // 直接PWM控制测试
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 700);  // 70%
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, 0);    // 0%
    HAL_Delay(2000);
    
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 0);    // 0%
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, 700);  // 70%
    HAL_Delay(2000);
    
    all_task_run();  // 保持其他任务运行
}
```

## 📋 检查清单总结

- [ ] 取消注释PID_Task
- [ ] 验证电机初始化成功
- [ ] 检查PWM信号输出
- [ ] 测试电源电压
- [ ] 验证硬件连接
- [ ] 检查驱动板状态
- [ ] 添加调试输出
- [ ] 逐步测试功能

**最可能的解决方案**: 取消注释`my_scheduler.c`中的`{PID_Task,5,0},`并确保PID_Task函数包含实际的电机控制逻辑。
