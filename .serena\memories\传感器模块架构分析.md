# 传感器模块架构分析

## BNO08X IMU传感器模块

### 硬件接口
- **通信方式**: I2C1接口
- **复位控制**: PD4引脚硬件复位
- **地址**: BNO080_DEFAULT_ADDRESS

### 功能特性
1. **9轴传感器融合**: 加速度计 + 陀螺仪 + 磁力计
2. **四元数输出**: 高精度姿态表示
3. **欧拉角转换**: Roll、Pitch、Yaw角度
4. **多种报告模式**: 旋转向量、线性加速度、步数计数等
5. **硬件复位功能**: 可靠的传感器恢复机制

### 初始化流程
1. I2C硬件初始化
2. 硬件复位 (优先) 或软件复位 (备用)
3. 启用旋转向量报告 (100ms间隔)
4. 可选启用其他传感器功能
5. 等待配置生效

### 数据处理
- **任务周期**: 10ms (可配置)
- **数据检查**: dataAvailable()轮询
- **四元数读取**: getQuatI/J/K/Real()
- **姿态转换**: QuaternionToEulerAngles()
- **精度监控**: 各传感器精度状态

## JY901S IMU传感器模块

### 硬件接口  
- **通信方式**: UART5串口
- **数据格式**: 专用协议数据包
- **接收方式**: DMA循环接收

### 功能特性
1. **备用IMU方案**: 与BNO08X互补
2. **串口通信**: 简单可靠的数据传输
3. **实时数据**: 连续数据流输出
4. **校准功能**: 支持传感器校准

### 数据处理流程
1. DMA接收原始数据到缓冲区
2. 接收完成回调处理数据包
3. JY901S_ProcessBuffer()解析协议
4. 重新启动DMA接收
5. 任务周期性读取处理结果

## 编码器模块

### 硬件接口
- **信号类型**: 正交编码器信号
- **定时器**: TIM3/TIM4编码器模式
- **分辨率**: 可配置脉冲数

### 功能特性
1. **速度测量**: 实时电机转速反馈
2. **位置测量**: 累计转动角度
3. **方向检测**: 正反转识别
4. **高精度**: 硬件定时器计数

## 灰度传感器模块

### 硬件接口
- **通信方式**: I2C2接口 (400kHz快速模式)
- **传感器阵列**: 多路灰度检测
- **数字输出**: 阈值比较结果

### 功能特性
1. **循迹检测**: 黑白线识别
2. **多点采样**: 传感器阵列覆盖
3. **实时处理**: 5ms任务周期
4. **数字化输出**: 简化后续处理

## 传感器数据融合

### 多IMU冗余
- BNO08X作为主要姿态传感器
- JY901S作为备用或验证传感器
- 可根据需要切换或融合数据

### 运动控制反馈
- 编码器提供速度/位置反馈
- IMU提供姿态和加速度信息
- 灰度传感器提供路径引导
- 多传感器协同实现精确控制

### 数据处理策略
1. **异步采集**: 各传感器独立采样
2. **周期性处理**: 任务调度器统一管理
3. **数据缓存**: 环形缓冲区平滑数据流
4. **错误处理**: 超时和异常检测机制