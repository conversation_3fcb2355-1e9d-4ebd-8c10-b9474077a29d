# 2024H BNO08X 米醋电控板项目架构分析

## 项目概述
这是一个基于STM32F407VET6微控制器的嵌入式控制系统项目，主要用于机器人或智能车的运动控制，集成了多种传感器和执行器。

## 硬件平台
- **主控芯片**: STM32F407VET6 (Cortex-M4, 168MHz)
- **开发环境**: Keil MDK-ARM
- **配置工具**: STM32CubeMX (.ioc文件)

## 目录结构分析

### 1. Core目录 - STM32 HAL层
- **Core/Inc/**: HAL库头文件，包含外设配置
- **Core/Src/**: HAL库源文件，包含main.c主程序
- 主要外设：DMA、GPIO、I2C、TIM、USART

### 2. Drivers目录 - 底层驱动
- **CMSIS/**: ARM Cortex-M4核心支持包
- **STM32F4xx_HAL_Driver/**: ST官方HAL驱动库

### 3. User目录 - 用户代码层（核心架构）
```
User/
├── App/           # 应用层 - 各功能模块的应用逻辑
├── Driver/        # 驱动层 - 硬件抽象驱动
├── Module/        # 模块层 - 第三方模块驱动
├── mydefine.h     # 全局定义文件
├── my_scheduler.c/h  # 任务调度器
└── my_timer.c/h   # 定时器管理
```

## 软件架构设计

### 1. 分层架构
```
应用层 (App)     - 业务逻辑实现
    ↓
驱动层 (Driver)  - 硬件抽象接口  
    ↓
模块层 (Module)  - 第三方设备驱动
    ↓
HAL层 (Core)     - STM32硬件抽象层
```

### 2. 任务调度系统
- **调度器**: `my_scheduler.c` 实现简单的时间片轮询调度
- **任务结构**: 包含任务函数指针、执行周期、上次执行时间
- **调度方式**: 基于HAL_GetTick()的非抢占式调度

### 3. 功能模块

#### 传感器模块
- **BNO08X**: 9轴IMU传感器，支持四元数、欧拉角输出
- **JY901S**: 备用IMU传感器，串口通信
- **编码器**: 电机速度反馈
- **灰度传感器**: 循迹检测

#### 执行器模块  
- **电机控制**: PWM驱动，支持速度控制
- **LED指示**: 状态显示
- **OLED显示**: 信息显示

#### 控制算法
- **PID控制**: 电机速度/位置控制
- **环形缓冲区**: 串口数据处理

## 通信接口
- **I2C1**: BNO08X传感器通信
- **I2C2**: 其他I2C设备（400kHz快速模式）
- **USART1**: 调试串口，DMA接收
- **UART5**: JY901S传感器专用串口
- **定时器**: TIM1/2/3/4用于PWM和编码器

## 项目特点
1. **模块化设计**: 清晰的分层架构，便于维护和扩展
2. **任务调度**: 简单高效的协作式多任务系统
3. **传感器融合**: 支持多种IMU传感器
4. **实时控制**: 基于定时器的精确控制
5. **调试友好**: 完善的串口调试输出系统

## 开发工具链
- **IDE**: Keil MDK-ARM
- **配置**: STM32CubeMX
- **调试**: ST-Link
- **版本控制**: 支持Git管理