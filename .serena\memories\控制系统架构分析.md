# 控制系统架构分析

## 电机控制系统

### 硬件配置
- **PWM输出**: 基于定时器TIM1/TIM2产生PWM信号
- **电机驱动**: 双电机独立控制 (左右轮)
- **方向控制**: GPIO控制电机正反转
- **速度控制**: PWM占空比调节电机转速

### 控制接口
```c
void Motor_Init(void);           // 电机初始化
void motor_set_l(float speed);   // 左电机速度设置
void motor_set_r(float speed);   // 右电机速度设置  
void motor_break(void);          // 电机制动
```

### 控制特点
1. **独立控制**: 左右电机可独立设置速度和方向
2. **平滑调速**: PWM方式实现无级调速
3. **制动功能**: 支持电机快速停止
4. **保护机制**: 防止电机过载和异常

## PID控制系统

### 控制算法
- **经典PID**: 比例(P) + 积分(I) + 微分(D)控制
- **参数可调**: Kp、Ki、Kd参数可配置
- **多回路**: 支持多个独立PID控制器

### 应用场景
1. **速度控制**: 电机转速闭环控制
2. **位置控制**: 电机位置精确控制
3. **姿态控制**: 基于IMU的平衡控制
4. **循迹控制**: 基于灰度传感器的路径跟踪

### 控制周期
- **任务周期**: 5ms高频控制
- **反馈源**: 编码器速度反馈
- **输出**: 电机PWM控制信号

## 编码器反馈系统

### 硬件配置
- **编码器接口**: TIM3/TIM4编码器模式
- **信号类型**: 正交编码器A/B相信号
- **计数方式**: 硬件自动计数，支持4倍频

### 反馈功能
```c
void Encoder_Init(void);    // 编码器初始化
void Encoder_Task(void);    // 编码器数据处理任务
```

### 测量参数
1. **转速测量**: 实时电机转速 (RPM)
2. **位置测量**: 累计转动角度
3. **方向检测**: 正反转方向识别
4. **距离计算**: 基于轮径的行驶距离

## 传感器融合控制

### 多传感器输入
- **IMU传感器**: 提供姿态角度 (Roll, Pitch, Yaw)
- **编码器**: 提供速度和位置反馈
- **灰度传感器**: 提供循迹路径信息
- **按键输入**: 提供用户控制指令

### 融合策略
1. **姿态控制**: IMU数据用于平衡和转向控制
2. **速度控制**: 编码器反馈用于精确速度控制
3. **路径控制**: 灰度传感器用于循迹导航
4. **安全控制**: 多传感器数据交叉验证

## 控制系统层次结构

### 高层控制 (应用层)
- 运动规划和路径规划
- 行为决策和模式切换
- 用户指令解析和执行

### 中层控制 (算法层)
- PID控制算法实现
- 传感器数据融合
- 控制参数自适应调整

### 底层控制 (驱动层)
- PWM信号生成
- 编码器计数读取
- GPIO状态控制

## 实时性保证

### 任务优先级
- **PID_Task**: 5ms高优先级控制任务
- **Encoder_Task**: 5ms编码器反馈任务
- **传感器任务**: 5-10ms传感器数据更新

### 时序要求
1. **控制周期**: 5ms确保控制系统稳定性
2. **反馈延迟**: 最小化传感器到控制器延迟
3. **执行精度**: PWM频率保证控制精度
4. **同步机制**: 任务调度器保证时序同步

## 控制模式

### 手动控制模式
- 按键直接控制电机运动
- 实时响应用户指令
- 安全保护和限制

### 自动控制模式  
- 基于传感器的自主导航
- PID闭环控制保证精度
- 异常检测和故障恢复

### 调试模式
- 参数在线调整
- 实时数据监控
- 性能分析和优化

## 系统特点
1. **多回路控制**: 支持多个独立控制回路
2. **实时响应**: 高频控制任务保证实时性
3. **精确反馈**: 编码器提供高精度反馈
4. **鲁棒性强**: 多传感器融合提高可靠性
5. **易于调试**: 完善的参数调整和监控机制