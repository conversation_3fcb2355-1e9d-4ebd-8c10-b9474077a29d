#include "motor_app.h"

Motor_t right_motor;
Motor_t left_motor; 

void Motor_Init(void)
{
  // 右电机：AIN1=TIM1_CH2, AIN2=TIM1_CH3, 反装
  Motor_Create(&left_motor, &htim1,
               TIM_CHANNEL_1, TIM_CHANNEL_2,           // AIN1, AIN2 PWM通道
               GPIOE, GPIO_PIN_9, GPIO_AF1_TIM1,       // AIN1 GPIO + AF
               GPIOE, GPIO_PIN_11, GPIO_AF1_TIM1,      // AIN2 GPIO + AF
               1);                                     // 反装

  // 左电机：AIN1=TIM1_CH4, AIN2=TIM1_CH1, 正装
  Motor_Create(&right_motor, &htim1,
               TIM_CHANNEL_3, TIM_CHANNEL_4,           // AIN1, AIN2 PWM通道
               GPIOE, GPIO_PIN_13, GPIO_AF1_TIM1,      // AIN1 GPIO + AF
               GPIOE, GPIO_PIN_14, GPIO_AF1_TIM1,      // AIN2 GPIO + AF
               0);                                     // 正装

  // 启动PWM通道
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_2);
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_3);
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_4);

  // 调试：验证电机初始化
  my_printf(&huart1, "Motor initialized\n");
  my_printf(&huart1, "Left motor state: %d\n", Motor_GetState(&left_motor));
  my_printf(&huart1, "Right motor state: %d\n", Motor_GetState(&right_motor));

  // 使能电机
  Motor_Enable(&left_motor, 1);
  Motor_Enable(&right_motor, 1);
}

//speed -100.0 ~ +100.0, 支持一位小数精度
void motor_set_l(float speed)
{
	Motor_SetSpeed(&left_motor, speed);
}

void motor_set_r(float speed)
{
	Motor_SetSpeed(&right_motor, speed);
}

void motor_break(void)
{
	Motor_Stop(&right_motor);
	Motor_Stop(&left_motor);
}

// 电机调试测试函数
void motor_debug_test(void) {
    my_printf(&huart1, "=== Motor Debug Test ===\n");

    // 测试左电机
    my_printf(&huart1, "Testing left motor...\n");
    motor_set_l(30.0f);
    HAL_Delay(2000);
    motor_set_l(0.0f);

    // 测试右电机
    my_printf(&huart1, "Testing right motor...\n");
    motor_set_r(30.0f);
    HAL_Delay(2000);
    motor_set_r(0.0f);

    my_printf(&huart1, "Test completed\n");
}
